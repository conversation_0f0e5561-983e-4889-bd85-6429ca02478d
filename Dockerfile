# syntax=docker/dockerfile:1

FROM python:3.12-slim

# Prevents Python from writing pyc files and keeps Python from buffering stdout/stderr
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime


WORKDIR /app



# Create a non-privileged user

# Install poetry
RUN pip install poetry==2.1.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
# Copy poetry configuration files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry sync

# Copy application code
COPY . .

# Switch to non-privileged user

# Expose the port the app runs on
EXPOSE 9999

# Command to run the application
CMD ["poetry", "run", "python", "-u", "main.py"]
