#%%
import akshare as ak
import pandas as pd
import requests
import json
#%%
## 千股千评
#%%
stock_comment_detail_zlkp_jgcyd_em_df = ak.stock_comment_detail_zlkp_jgcyd_em(symbol="600000")
sorted_df = stock_comment_detail_zlkp_jgcyd_em_df.sort_values(by='交易日', ascending=False)
#%%
sorted_df
#%%
sorted_df['交易日'] = pd.to_datetime(sorted_df['交易日'])
target_date = pd.to_datetime('2025-04-28')
result = sorted_df.loc[sorted_df['交易日'] == target_date, '机构参与度'].iloc[0]
print(result)
#%%
stock_comment_detail_scrd_focus_em_df = ak.stock_comment_detail_scrd_focus_em(symbol="600000")
stock_comment_detail_scrd_focus_em_df
#%%
# -------
#%%
# 个股资金流
import akshare as ak

stock_individual_fund_flow_df = ak.stock_individual_fund_flow(stock="002131", market="sz")
stock_individual_fund_flow_df
#%%
from datetime import datetime, timedelta
datetime.now() - timedelta(days=0)
#%%
# A 股市场开市日历
ak.tool_trade_date_hist_sina()
#%%
# -------
#%%
# 获取行业板块
stock_board_industry_name_em_df = ak.stock_board_industry_name_em()
stock_board_industry_name_em_df
#%%
# ------
#%%
# 板块资金流 	indicator="今日"; choice of {"今日", "5日", "10日"}
stock_sector_fund_flow_rank_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")
stock_sector_fund_flow_rank_df
#%%
# ------
#%%
# 概念资金流
stock_fund_flow_concept_df = ak.stock_fund_flow_concept(symbol="即时")
stock_fund_flow_concept_df
#%%
# -----
#%%
# 行业成分股 symbol="小金属"; 支持传入板块代码比如：BK1027，可以通过调用 ak.stock_board_industry_name_em() 查看东方财富-行业板块的所有行业代码
stock_board_industry_cons_em_df = ak.stock_board_industry_cons_em(symbol="BK1027")
stock_board_industry_cons_em_df
#%%
# ------
#%%
# 概念板块
stock_board_concept_name_em_df = ak.stock_board_concept_name_em()
stock_board_concept_name_em_df
#%%
# 概念板块成分股
stock_board_concept_cons_em_df = ak.stock_board_concept_cons_em(symbol="BK1184")
stock_board_concept_cons_em_df
#%%
# --------
#%%
# 指数 symbol="上证系列指数"；choice of {"沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"}
stock_zh_index_spot_em_df = ak.stock_zh_index_spot_em(symbol="沪深重要指数")
stock_zh_index_spot_em_df
#%%
# 指数历史行情数据
# symbol	str	symbol="399282"; 指数代码，此处不用市场标识
# period	str	period="daily"; choice of {'daily', 'weekly', 'monthly'}
# start_date	str	start_date="19700101"; 开始日期
# end_date	str	end_date="22220101"; 结束时间
index_zh_a_hist_df = ak.index_zh_a_hist(symbol="000001", period="daily", start_date="20190101", end_date="20250509")
index_zh_a_hist_df
#%%
# -----
#%%
# 指数分时行情
# symbol	str	symbol="399006"; 指数代码，此处不用市场标识
# period	str	period="1"; choice of {'1', '5', '15', '30', '60'}, 其中 1 分钟数据只能返回当前的, 其余只能返回近期的数据
# start_date	str	start_date="1979-09-01 09:32:00"; 开始日期时间
# end_date	str	end_date="2222-01-01 09:32:00"; 结束时间时间
#%%
index_zh_a_hist_min_em_df = ak.index_zh_a_hist_min_em(symbol="000001", period="60", start_date="2025-02-25 09:30:00", end_date="2025-04-30 15:00:00")
index_zh_a_hist_min_em_df
#%%
# ------
#%%
# 港股实时行情数据
stock_hk_index_spot_em_df = ak.stock_hk_index_spot_em()
stock_hk_index_spot_em_df.head(50)
#%%
# -------
#%%
# 港股历史行情数据
stock_zh_index_daily_em_df = ak.stock_hk_index_daily_em(symbol="HSTECH")
stock_zh_index_daily_em_df
#%%
# ---------
#%%
# 美股指数 symbol=".INX"; choice of {".IXIC", ".DJI", ".INX", ".NDX"}

index_us_stock_sina_df = ak.index_us_stock_sina(symbol=".INX")
index_us_stock_sina_df
#%%
# ---------
#%%
# 国债收益率
bond_zh_us_rate_df = ak.bond_zh_us_rate(start_date="20250429")
bond_zh_us_rate_df
#%%
# ----------
#%%
# 市盈率
stock_index_pe_lg_df = ak.stock_index_pe_lg(symbol="中证500")
stock_index_pe_lg_df
#%%
sorted_df = stock_index_pe_lg_df.sort_values(by="静态市盈率", ascending=True)
sorted_df.head(50)
#%%
import numpy as np

def calculate_equity_risk_premium(pe_ratio, bond_yield):
    """
    计算股权溢价指数（Equity Risk Premium, ERP）
    
    参数：
    pe_ratio (float): 市场市盈率（PE Ratio）
    bond_yield (float): 无风险收益率（如10年期国债收益率）
    
    返回：
    float: 股权溢价指数（ERP = 1/PE Ratio - Bond Yield）
    
    注：通常PE使用市场整体市盈率，bond_yield使用长期国债收益率
    """
    if pe_ratio <= 0:
        raise ValueError("PE比率必须为正数")
    earnings_yield = 1 / pe_ratio
    erp = earnings_yield - bond_yield
    return erp

def determine_investment_interval(historical_erp, high_percentile=70):
    """
    根据历史ERP数据确定适合定投的区间
    
    参数：
    historical_erp (list): 历史股权溢价指数列表
    high_percentile (int): 高分位阈值（0-100），默认70%（当ERP高于该分位数时适合定投）
    
    返回：
    tuple: 适合定投的区间（下限， 上限），格式为（阈值，正无穷）
    
    注：high_percentile=70表示当ERP高于历史70%分位数时适合定投
    """
    if not historical_erp:
        raise ValueError("历史ERP数据不能为空")
    threshold = np.percentile(historical_erp, high_percentile)
    return (threshold, np.inf)

# ---------------------- 使用示例 ----------------------
if __name__ == "__main__":
    # 示例数据（单位：百分比，需转换为小数）
    historical_erp_data = [0.03, 0.04, 0.05, 0.06, 0.07, 0.02, 0.03, 0.04, 0.05, 0.06]
    current_pe = 20          # 当前市场市盈率
    current_bond_yield = 0.025  # 当前国债收益率（2.5%）

    # 计算当前股权溢价指数
    current_erp = calculate_equity_risk_premium(current_pe, current_bond_yield)
    print(f"当前股权溢价指数：{current_erp:.4f}")

    # 计算定投区间（取历史70%分位数）
    investment_lower, _ = determine_investment_interval(
        historical_erp_data, 
        high_percentile=70
    )
    print(f"适合定投的股权溢价指数区间：({investment_lower:.4f}, ∞)")

    # 判断当前是否适合定投
    if current_erp >= investment_lower:
        print("结论：当前适合进行定投")
    else:
        print("结论：当前不适合定投")

#%%
# ---------
#%%
# 外盘期货列表
futures_hq_subscribe_exchange_symbol_df = ak.futures_hq_subscribe_exchange_symbol()
futures_hq_subscribe_exchange_symbol_df
#%%
# -------
#%%
# 外盘期货实时行情
futures_foreign_commodity_realtime_df = ak.futures_foreign_commodity_realtime(symbol='XAU,GC')
futures_foreign_commodity_realtime_df
#%%
# --------
#%%
# 查询退市
stock_zh_a_stop_em_df = ak.stock_zh_a_stop_em()
stock_zh_a_stop_em_df
#%%
# -------
#%%
# 沪市
index_hog_spot_price_df = ak.stock_sh_a_spot_em()
index_hog_spot_price_df.keys()
#%%
# 深圳市场
stock_sz_a_spot_em_df = ak.stock_sz_a_spot_em()
stock_sz_a_spot_em_df
#%%
df = ak.stock_individual_info_em(symbol="000018")
df
#%%
# --------
#%%
# ETF 列表
fund_name_em_df = ak.fund_name_em()
fund_name_em_df
#%%
# ------
#%%
# 港股实时行情（延时15分钟）
stock_hk_main_board_spot_em_df = ak.stock_hk_main_board_spot_em()
stock_hk_main_board_spot_em_df
#%%
# 计算 MA15
df = ak.stock_zh_a_hist_min_em(symbol="000001", start_date="2025-05-23 15:00:00", end_date="2025-05-23 15:05:00", period="1", adjust="qfq")
df
#%%
import pandas as pd
from pyecharts.charts import Line
from pyecharts import options as opts

# 计算均线
df["MA15"] = df["收盘"].rolling(window=15).mean()
df["MA30"] = df["收盘"].rolling(window=30).mean()
df["MA60"] = df["收盘"].rolling(window=60).mean()
df["MA120"] = df["收盘"].rolling(window=120).mean()

# 绘图
from pyecharts.charts import Line
from pyecharts import options as opts

line = (
    Line()
    .add_xaxis(df["时间"].astype(str).tolist())
    .add_yaxis("收盘价", df["收盘"].round(2).tolist(), is_smooth=True, label_opts=opts.LabelOpts(is_show=False))
    .add_yaxis("MA15", df["MA15"].round(2).tolist(), is_smooth=True, label_opts=opts.LabelOpts(is_show=False))
    .add_yaxis("MA30", df["MA30"].round(2).tolist(), is_smooth=True, label_opts=opts.LabelOpts(is_show=False))
    .add_yaxis("MA60", df["MA60"].round(2).tolist(), is_smooth=True, label_opts=opts.LabelOpts(is_show=False))
    .add_yaxis("MA120", df["MA120"].round(2).tolist(), is_smooth=True, label_opts=opts.LabelOpts(is_show=False))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="收盘价与均线对比 (MA15 / MA30 / MA60 / MA120)"),
        tooltip_opts=opts.TooltipOpts(trigger="axis"),
        datazoom_opts=[opts.DataZoomOpts(), opts.DataZoomOpts(type_="inside")],
        xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False),
    )
)

# 如果你在 notebook 中：
# line.render_notebook()

# 或者输出 HTML 文件查看
line.render("ma_comparison.html")

#%%
# etf 行情数据
fund_etf_spot_em_df = ak.fund_etf_spot_em()
fund_etf_spot_em_df
#%%
# 外盘期货行情
df = ak.futures_global_spot_em()
df
#%%
df[df["名称"] == "COMEX黄金"]
#%%
# 期货历史行情
futures_global_hist_em_df = ak.futures_global_hist_em(symbol="HG00Y")
futures_global_hist_em_df
#%%
ak.futures_hq_subscribe_exchange_symbol()
#%%
futures_foreign_commodity_realtime_df = ak.futures_foreign_commodity_realtime(symbol='GC')
futures_foreign_commodity_realtime_df
#%%
# ------
#%%
ak.index_global_spot_em()
# ak.index_us_stock_sina(symbol=".INX")
#%%
# -------
#%%
# etf 日线
fund_etf_hist_em_df = ak.fund_etf_hist_em(symbol="513500", period="daily", start_date="20250401", end_date="20250505", adjust="")
result = fund_etf_hist_em_df.iloc[-1]["收盘"]
print(result)
#%%
# --------
#%%
index_zh_a_hist_min_em_df = ak.index_zh_a_hist_min_em(symbol="600000", period="60", start_date="2025-05-01 09:30:00", end_date="2025-05-07 15:30:00")
index_zh_a_hist_min_em_df
#%%
index_zh_a_hist_min_em_df["时间"]
#%%
from datetime import datetime
resp = []
for _, row in index_zh_a_hist_min_em_df.iterrows():
    print(row)
    print(datetime.strptime(
                row["时间"], "%Y-%m-%d %H:%M:%S"
            ))
    break
#%%
ak.tool_trade_date_hist_sina()
#%%
