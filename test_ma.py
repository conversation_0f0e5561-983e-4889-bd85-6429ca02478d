from datetime import datetime, timedelta
import pandas as pd
from src.domain.kline.aggregate.zh.ma import ZhStockMoveAverageAggregate

def main():
    # 创建移动平均线聚合对象
    ma_aggregate = ZhStockMoveAverageAggregate()
    
    # 设置测试参数
    code = "000001"  # 平安银行
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)  # 获取最近30天的数据
    
    print(f"测试股票代码: {code}")
    print(f"日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    # 测试日K线的MA5
    print("\n测试日K线MA5:")
    df_daily_ma5 = ma_aggregate.list_daily_ma5(code, start_date, end_date)
    if not df_daily_ma5.empty:
        print(f"获取到 {len(df_daily_ma5)} 条数据")
        print(df_daily_ma5[['close', 'MA5']].tail())
    else:
        print("未获取到数据")
    
    # 测试30分钟K线的MA30
    print("\n测试30分钟K线MA30:")
    df_30min_ma30 = ma_aggregate.list_30min_ma30(code, start_date, end_date)
    if not df_30min_ma30.empty:
        print(f"获取到 {len(df_30min_ma30)} 条数据")
        print(df_30min_ma30[['close', 'MA30']].tail())
    else:
        print("未获取到数据")
    
    # 测试60分钟K线的MA60
    print("\n测试60分钟K线MA60:")
    df_60min_ma60 = ma_aggregate.list_60min_ma60(code, start_date, end_date)
    if not df_60min_ma60.empty:
        print(f"获取到 {len(df_60min_ma60)} 条数据")
        print(df_60min_ma60[['close', 'MA60']].tail())
    else:
        print("未获取到数据")

if __name__ == "__main__":
    main()
