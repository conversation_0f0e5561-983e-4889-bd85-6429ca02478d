import os

from langgraph.constants import END
from langgraph.graph import StateGraph
from langgraph.types import interrupt
from pydantic import BaseModel
from typing import Dict, Any, Optional

# 定义状态
class State(BaseModel):
    user_input: str
    user_id: Optional[str] = None
    intent: Optional[str] = None
    reply: Optional[str] = None
    logs: Dict[str, Any] = {}

# --------------------------
# 子图定义（Subgraph）
# --------------------------
subgraph_builder = StateGraph(State)

# 子图节点1：分析意图
def analyze_intent(state: State) -> State:
    if "退款" in state.user_input:
        state.intent = "退款"
    else:
        state.intent = "常规咨询"
    return state

# 子图节点2：生成回复
def generate_reply(state: State) -> State:
    if state.intent == "退款":
        state.reply = "请提供订单号，我们将处理退款。"
    else:
        state.reply = "请问有什么可以帮您？"
    return state

# 子图节点3：校验回复
def validate_reply(state: State) -> State:
    if not state.reply:
        raise ValueError("回复内容不能为空")
    return state

# 添加子图节点和边
subgraph_builder.add_node("analyze_intent", analyze_intent)
subgraph_builder.add_node("generate_reply", generate_reply)
subgraph_builder.add_node("validate_reply", validate_reply)
subgraph_builder.add_edge("analyze_intent", "generate_reply")
subgraph_builder.add_edge("generate_reply", "validate_reply")
subgraph_builder.set_entry_point("analyze_intent")
subgraph_builder.add_edge("validate_reply", END)
subgraph = subgraph_builder.compile()


# --------------------------
# 主图定义（Main Graph）
# --------------------------
maingraph = StateGraph(State)

# 主图节点1：验证用户
def verify_user(state: State) -> State:
    state.user_id = "user_123"
    return state

# 主图节点3：记录日志
def log_activity(state: State) -> State:
    state.logs = {
        "user_id": state.user_id,
        "intent": state.intent,
        "reply": state.reply
    }
    return state

# 添加主图节点和边
maingraph.add_node("verify_user", verify_user)
maingraph.add_node("subgraph_node", subgraph)  # 直接添加子图作为节点
maingraph.add_node("log_activity", log_activity)
maingraph.add_edge("verify_user", "subgraph_node")
maingraph.add_edge("subgraph_node", "log_activity")
maingraph.set_entry_point("verify_user")
maingraph.add_edge("log_activity", END)

# --------------------------
# 运行流程
# --------------------------
initial_state = State(user_input="我想申请退款")
g = maingraph.compile()

# 可选：生成图表（使用本地渲染方法避免超时）
# 如果需要生成图表，取消下面的注释

import os
from langchain_core.runnables.graph_mermaid import MermaidDrawMethod

# 生成主图的流程图
png_path = os.path.dirname(os.path.abspath(__file__)) + "/" + "workflow.png"
g.get_graph().draw_mermaid_png(
    output_file_path=png_path,
    draw_method=MermaidDrawMethod.PYPPETEER,  # 使用本地浏览器渲染
    max_retries=5,
    retry_delay=2.0,
    expand_subgraphs=True
)

# 生成子图的流程图
subgraph_png_path = os.path.dirname(os.path.abspath(__file__)) + "/" + "subgraph.png"
subgraph.get_graph().draw_mermaid_png(
    output_file_path=subgraph_png_path,
    draw_method=MermaidDrawMethod.PYPPETEER,
    max_retries=5,
    retry_delay=2.0
)
interrupt()


# # 运行图并打印结果
res = g.invoke(initial_state)
print(res)

