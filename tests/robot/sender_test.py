from config.globalconfig import get_or_create_settings_ins
from src.domain.communicate.aggregate.sender import MessageSenderAggregate

sender = MessageSenderAggregate()

# 
# config = get_or_create_settings_ins()
# print(config.lark.trading.user_id)


# sender.notice("test", "test")
# sender.notice_success("test", "test")
# sender.notice_fail("test", "test")
# sender.cron_job_alert("test", "test")
# sender.log_alert("test", "test")


def asd():
    print(f"{asd.__name__}")
    
asd()