#!/bin/bash

# 配置参数
PROCESS_NAME="yunshumanager"  # 监控的进程名（不区分大小写）
THRESHOLD=10.0                # CPU 告警阈值（百分比）
LOG_FILE="/tmp/${PROCESS_NAME}_cpu_alert.log"  # 日志路径

# 获取所有匹配进程的 CPU 使用率（兼容 macOS/Linux）
cpu_usages=$(ps aux | grep -i "$PROCESS_NAME" | grep -v grep | awk '{print $3}')

# 检查是否有匹配的进程
if [ -z "$cpu_usages" ]; then
  echo "$(date) - 错误：未找到进程 '$PROCESS_NAME'" | tee -a "$LOG_FILE"
  exit 1
fi

# 遍历每个进程的 CPU 使用率
triggered=false
for cpu in $cpu_usages; do
  # 判断是否超过阈值（支持小数比较）
  if [ $(echo "$cpu >= $THRESHOLD" | bc) -eq 1 ]; then
    message="$(date) - 警告：进程 '$PROCESS_NAME' CPU 使用率 ${cpu}% ≥ ${THRESHOLD}%"
    echo -e "\e[31m$message\e[0m"  # 终端显示红色告警
    echo "$message" >> "$LOG_FILE"
    triggered=true
  fi
done

# 发送系统通知（仅当有进程触发告警时）
if [ "$triggered" = true ]; then
  if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS 通知（需终端有辅助功能权限）
    osascript -e "display notification \"检测到 CPU 使用率超过阈值\" with title \"进程告警\" subtitle \"$PROCESS_NAME\""
  else
    # Linux 桌面通知（需安装 libnotify-bin）
    export DISPLAY=:0  # 确保在 GUI 环境下显示
    notify-send "进程告警" "进程 $PROCESS_NAME CPU 使用率 ≥ ${THRESHOLD}%" --urgency=critical
  fi
fi
