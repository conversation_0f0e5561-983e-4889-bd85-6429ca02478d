#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入脚本: 将多个数据目录下的股票数据批量导入到 KLine60Min 表中
支持特性:
1. 支持多个数据目录输入，依次处理每个目录
2. 每个目录中的文件结构相同，都是 CSV 格式
3. 批量处理文件，避免重复插入相同的记录
4. 每个目录处理完成后输出详细的统计信息
"""

import os
import glob
import pandas as pd
from datetime import datetime
from tqdm import tqdm
import logging
from pathlib import Path
import time
from typing import List, Dict, Set, Tuple

# 导入项目相关模块
from src.domain.store.repo.dao import KLine60Min, KLine60Min
from src.infra.clients.mysql.orm import DBSessionContext, bulk_insert
from src.infra.app import app
from config.globalconfig import get_or_create_settings_ins

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("import_kline")

# 数据目录列表 - 可以配置多个目录，程序会依次处理每个目录下的CSV文件
DATA_DIRS = [
    "/Users/<USER>/Movies/60min/2020",
    "/Users/<USER>/Movies/60min/2021",
    "/Users/<USER>/Movies/60min/2022",
    "/Users/<USER>/Movies/60min/2023",
    "/Users/<USER>/Movies/60min/2024",
    "/Users/<USER>/Movies/60min/2025",
]
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
BATCH_SIZE = 5000  # 每批处理的记录数
FILES_PER_BATCH = 30  # 每批处理的文件数


def extract_stock_code(filename):
    """从文件名中提取股票代码"""
    basename = os.path.basename(filename)
    parts = basename.split('.')
    if len(parts) >= 3:
        market = parts[0]  # SH or SZ
        code = parts[1]
        return code
    return None


def process_csv_file(file_path):
    """处理单个CSV文件并返回KLine60Min对象列表"""
    stock_code = extract_stock_code(file_path)
    if not stock_code:
        return [], f"无法从文件名提取股票代码: {file_path}"

    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)

        # 检查必要的列是否存在
        required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'amount']
        if not all(col in df.columns for col in required_columns):
            return [], f"文件缺少必要的列: {file_path}"

        # 创建KLine60Min对象列表
        klines = []
        for _, row in df.iterrows():
            try:
                kline = KLine60Min(
                    code=stock_code,
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume'],
                    amount=row['amount'],
                    datetime=datetime.strptime(row['datetime'], DATETIME_FORMAT)
                )
                klines.append(kline)
            except Exception as e:
                return [], f"处理行数据时出错: {e}, 文件: {file_path}, 行: {row}"

        return klines, None
    except Exception as e:
        return [], f"处理文件时出错: {e}, 文件: {file_path}"


def get_existing_keys_for_codes(session, stock_codes):
    """批量获取数据库中已存在的记录键值 (code, datetime)"""
    existing_records = session.query(KLine60Min.code, KLine60Min.datetime).filter(
        KLine60Min.code.in_(stock_codes)
    ).all()
    return {(record.code, record.datetime) for record in existing_records}


def process_file_batch(file_batch):
    """处理一批文件并返回处理结果"""
    all_klines = []
    stock_codes = set()
    failed_files = []

    # 处理每个文件
    for file_path in file_batch:
        stock_code = extract_stock_code(file_path)
        if not stock_code:
            failed_files.append((file_path, "无法从文件名提取股票代码"))
            continue

        stock_codes.add(stock_code)
        klines, error = process_csv_file(file_path)

        if error:
            failed_files.append((file_path, error))
        else:
            all_klines.extend(klines)

    return all_klines, stock_codes, failed_files


def main():
    """主函数"""
    start_time = time.time()
    logger.info("开始导入 KLine60Min 数据...")

    # 统计计数器
    total_files = 0
    processed_files = 0
    total_records = 0
    inserted_records = 0
    failed_files_list = []

    # 依次处理每个数据目录
    for dir_index, data_dir in enumerate(DATA_DIRS):
        dir_start_time = time.time()
        logger.info(f"开始处理目录 [{dir_index+1}/{len(DATA_DIRS)}]: {data_dir}")

        # 获取当前目录下的所有CSV文件
        csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
        dir_file_count = len(csv_files)

        if dir_file_count == 0:
            logger.warning(f"目录 {data_dir} 中未找到CSV文件，跳过")
            continue

        logger.info(f"在目录 {data_dir} 中找到 {dir_file_count} 个CSV文件")

        # 记录当前目录的文件总数
        dir_processed_files = 0
        dir_total_records = 0
        dir_inserted_records = 0

        # 按批次处理当前目录下的文件
        for i in range(0, dir_file_count, FILES_PER_BATCH):
            batch_start_time = time.time()
            file_batch = csv_files[i:i+FILES_PER_BATCH]
            batch_size = len(file_batch)

            logger.info(f"开始处理目录 {data_dir} 的第 {i//FILES_PER_BATCH + 1} 批文件 ({i+1}-{min(i+batch_size, dir_file_count)}/{dir_file_count})")

            # 处理一批文件
            all_klines, stock_codes, batch_failed_files = process_file_batch(file_batch)
            failed_files_list.extend(batch_failed_files)

            dir_total_records += len(all_klines)
            dir_processed_files += batch_size - len(batch_failed_files)

            if all_klines:
                # 批量插入数据库，避免重复记录
                with app.orm_session() as session:
                    try:
                        # 获取已存在的记录键值
                        existing_keys = get_existing_keys_for_codes(session, stock_codes)

                        # 过滤出新记录
                        new_klines = []
                        for kline in all_klines:
                            key = (kline.code, kline.datetime)
                            if key not in existing_keys:
                                new_klines.append(kline)

                        # 批量插入新记录
                        if new_klines:
                            # 分批处理以避免一次性插入过多数据
                            for j in range(0, len(new_klines), BATCH_SIZE):
                                batch = new_klines[j:j+BATCH_SIZE]
                                bulk_insert(session, KLine60Min, batch)

                            session.commit()
                            dir_inserted_records += len(new_klines)

                            logger.info(f"批次处理完成: 总记录 {len(all_klines)}, 新插入 {len(new_klines)}")
                        else:
                            logger.info(f"批次处理完成: 所有记录已存在")

                    except Exception as e:
                        session.rollback()
                        logger.error(f"插入数据时出错: {e}")
                        for file_path in file_batch:
                            failed_files_list.append((file_path, f"数据库插入错误: {e}"))

            batch_end_time = time.time()
            logger.info(f"批次处理耗时: {batch_end_time - batch_start_time:.2f} 秒")
            logger.info(f"目录进度: {min(i+batch_size, dir_file_count)}/{dir_file_count} 文件 ({min(i+batch_size, dir_file_count)/dir_file_count*100:.1f}%)")

        # 更新总统计信息
        total_records += dir_total_records
        processed_files += dir_processed_files
        inserted_records += dir_inserted_records

        dir_end_time = time.time()
        logger.info(f"目录 {data_dir} 处理完成")
        logger.info(f"目录处理耗时: {dir_end_time - dir_start_time:.2f} 秒")
        logger.info(f"目录处理成功文件数: {dir_processed_files}/{dir_file_count}")
        logger.info(f"目录总记录数: {dir_total_records}")
        logger.info(f"目录新插入记录数: {dir_inserted_records}")

    # 输出最终统计信息
    end_time = time.time()
    logger.info("所有数据目录处理完成!")
    logger.info(f"总耗时: {end_time - start_time:.2f} 秒")
    logger.info(f"处理目录数: {len(DATA_DIRS)}")
    logger.info(f"处理成功文件数: {processed_files}/{total_files}")
    logger.info(f"总记录数: {total_records}")
    logger.info(f"新插入记录数: {inserted_records}")

    # 输出处理失败的文件
    if failed_files_list:
        logger.error(f"处理失败的文件数: {len(failed_files_list)}")
        logger.error("处理失败的文件列表:")
        for file_path, error in failed_files_list:
            logger.error(f"  - {Path(file_path).name}: {error}")
    else:
        logger.info("所有文件处理成功!")


if __name__ == "__main__":
    main()
