from datetime import datetime, date
from typing import Literal

from pydantic import Field, BaseModel



class SyncOneStockKlineReq(BaseModel):
    code: str = Field(..., description="stock code")
    start_date: datetime = Field(..., description="start datetime")
    end_date: datetime = Field(..., description="end datetime")


class SyncAllStockKlineReq(BaseModel):
    start_date: datetime = Field(..., description="start datetime")
    end_date: datetime = Field(..., description="end datetime")
    
class SyncStockFundFlowReq(BaseModel):
    code: str = Field(..., description="stock code")
    before_days: int = Field(..., description="before days")
    
class SyncStockMarginTradingReq(BaseModel):
    code: str = Field(..., description="stock code")
    start_date: date = Field(..., description="start date")
    end_date: date = Field(..., description="end date")

class SyncStockUMAReq(BaseModel):
    code: str = Field(..., description="stock code")
    before_days: int = Field(..., description="before days")


class AddIndexReq(BaseModel):
    code: str = Field(..., description="index code")
    choice_type: Literal[
        "沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"
    ] = Field(..., description="choice type")


class DeleteIndexReq(BaseModel):
    code: str = Field(..., description="index code")


class SyncIndexKlineReq(BaseModel):
    start_date: date = Field(..., description="start date")
    end_date: date = Field(..., description="end date")


class IsConceptReq(BaseModel):
    is_concept: bool = Field(..., description="is concept")

