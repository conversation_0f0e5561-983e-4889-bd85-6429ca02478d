from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Union

import pandas as pd

from src.domain.store.repo import dao
from src.domain.store.service.zh import ZhStockStoreService


class ZhStockMoveAverageAggregate:

    def __init__(self):
        self._zh_stock_store_svc = ZhStockStoreService()

    def _convert_kline_to_dataframe(self, klines: List[Union[dao.KLine30Min, dao.KLine60Min, dao.KLineDaily]]) -> pd.DataFrame:
        """
        将K线数据转换为DataFrame
        """
        if not klines:
            return pd.DataFrame()

        # 检查是否有datetime属性（分钟K线）或date属性（日K线）
        date_field = 'datetime' if hasattr(klines[0], 'datetime') else 'date'

        data = [
            {
                'code': item.code,
                date_field: getattr(item, date_field),
                'open': float(item.open) if item.open else None,
                'high': float(item.high) if item.high else None,
                'low': float(item.low) if item.low else None,
                'close': float(item.close) if item.close else None,
                'volume': float(item.volume) if item.volume else None,
                'amount': float(item.amount) if item.amount else None
            }
            for item in klines
        ]

        df = pd.DataFrame(data)
        if not df.empty:
            # 设置日期/时间为索引，并按时间排序
            df.set_index(date_field, inplace=True)
            df.sort_index(inplace=True)

        return df

    def _calculate_ma(self, df: pd.DataFrame, window: int, column: str = 'close') -> pd.DataFrame:
        """
        计算移动平均线

        Args:
            df: 包含K线数据的DataFrame
            window: 移动平均窗口大小
            column: 用于计算移动平均的列名，默认为'close'（收盘价）

        Returns:
            添加了移动平均列的DataFrame
        """
        if df.empty:
            return df

        # 计算移动平均线
        ma_column = f'MA{window}'
        df[ma_column] = df[column].rolling(window=window, min_periods=1).mean()

        return df

    def _get_extended_date_range(self, start_date: datetime, end_date: datetime, window: int, period_minutes: int = None) -> tuple:
        """
        扩展日期范围以确保有足够的数据计算移动平均线

        Args:
            start_date: 原始开始日期
            end_date: 原始结束日期
            window: 移动平均窗口大小
            period_minutes: 周期分钟数（用于分钟K线）

        Returns:
            扩展后的(start_date, end_date)元组
        """
        if period_minutes:  # 分钟K线
            # 估算需要额外的交易日数量（假设每天有4小时交易时间）
            trading_periods_per_day = 4 * 60 // period_minutes  # 每天的交易周期数
            extra_days = (window // trading_periods_per_day) + 2  # 加2天作为缓冲
            extended_start = start_date - timedelta(days=extra_days)
        else:  # 日K线
            # 对于日K线，直接向前推window个交易日（加上额外的缓冲）
            extended_start = start_date - timedelta(days=window * 1.5)  # 乘以1.5作为缓冲

        return extended_start, end_date

    def list_30min_ma5(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA5
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 5, 30)

        # 获取30分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_30min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA5
        df = self._calculate_ma(df, 5)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    # 30分钟K线的移动平均线方法
    def list_30min_ma30(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA30
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 30, 30)

        # 获取30分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_30min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA30
        df = self._calculate_ma(df, 30)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_30min_ma60(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA60
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 60, 30)

        # 获取30分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_30min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA60
        df = self._calculate_ma(df, 60)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_30min_ma120(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA120
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 120, 30)

        # 获取30分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_30min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA120
        df = self._calculate_ma(df, 120)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df
    
    def list_60min_ma5(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA5
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 5, 60)

        # 获取60分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_60min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA5
        df = self._calculate_ma(df, 5)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df
    
    # 60分钟K线的移动平均线方法
    def list_60min_ma30(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA30
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 30, 60)

        # 获取60分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_60min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA30
        df = self._calculate_ma(df, 30)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_60min_ma60(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA60
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 60, 60)

        # 获取60分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_60min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA60
        df = self._calculate_ma(df, 60)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_60min_ma120(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA120
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 120, 60)

        # 获取60分钟K线数据
        klines = self._zh_stock_store_svc.get_stock_60min_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA120
        df = self._calculate_ma(df, 120)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    # 日K线的移动平均线方法
    def list_daily_ma5(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA5
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 5)

        # 获取日K线数据
        klines = self._zh_stock_store_svc.get_stock_daily_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA5
        df = self._calculate_ma(df, 5)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_daily_ma30(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA30
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 30)

        # 获取日K线数据
        klines = self._zh_stock_store_svc.get_stock_daily_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA30
        df = self._calculate_ma(df, 30)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_daily_ma60(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA60
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 60)

        # 获取日K线数据
        klines = self._zh_stock_store_svc.get_stock_daily_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA60
        df = self._calculate_ma(df, 60)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_daily_ma120(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA120
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 120)

        # 获取日K线数据
        klines = self._zh_stock_store_svc.get_stock_daily_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA120
        df = self._calculate_ma(df, 120)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df

    def list_daily_ma250(self, code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        # 扩展日期范围以确保有足够的数据计算MA250
        extended_start, extended_end = self._get_extended_date_range(start_date, end_date, 250)

        # 获取日K线数据
        klines = self._zh_stock_store_svc.get_stock_daily_kline(code, extended_start, extended_end)

        # 转换为DataFrame
        df = self._convert_kline_to_dataframe(klines)

        # 计算MA250
        df = self._calculate_ma(df, 250)

        # 过滤回原始日期范围
        if not df.empty:
            df = df[(df.index >= start_date) & (df.index <= end_date)]

        return df


class ZhEtfMoveAverage:
    pass
