import json
from typing import Optional, Dict, Any

from lark_oapi.api.im.v1 import CreateMessageRequest, CreateMessageRequestBody, CreateMessageResponse, \
    CreateMessageResponseBody
from loguru import logger

from src.infra import clients
from src.infra.app import app
from src.infra.clients.lark.client import get_lark_client, LarkClientType




class MessageSenderAggregate:
    
    
    def _send_card(self, template_id: str, template_variable: Dict[str, Any], client_type: LarkClientType) -> Optional[CreateMessageResponseBody]:
        for k, v in app.config.lark.__dict__.items():
            if k == client_type.value:
                user_id = v.user_id
                lark_client = get_lark_client(client_type)
                break
        else:
            raise ValueError(f"不支持的Lark客户端类型: {client_type}")
        
        content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": template_variable,
            },
        }
        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type("user_id")
            .request_body(
                CreateMessageRequestBody.builder()
                .content(content=json.dumps(content))
                .msg_type(msg_type="interactive")
                .receive_id(user_id)
                .build()
            )
            .build()
        )

        # 发起请求
        response: CreateMessageResponse = lark_client.im.v1.message.create(request)

        if response.success:
            return response.data
        logger.error(f"Failed to send message: {response.msg}")
        return None
    
    def _get_card_tmpl_id(self, client_type: LarkClientType, tmpl_name: str) -> str:
        for k, v in app.config.lark.__dict__.items():
            if k == client_type.value:
                return v.__dict__[tmpl_name]
        else:
            raise ValueError(f"不支持的Lark客户端类型: {client_type}")
    
    def notice(self, sub_title: str, content: str):
        template_variable = {
            "sub_title": sub_title,
            "content": content,
        }
        # trading
        self._send_card(
            self._get_card_tmpl_id(LarkClientType.trading, "notice_tmpl_id"),
            template_variable,
            LarkClientType.trading,
        )
        # tt
        pass
    
    def notice_success(self, sub_title: str, content: str):
        template_variable = {
            "sub_title": sub_title,
            "content": content,
        }
        # trading
        self._send_card(
            self._get_card_tmpl_id(LarkClientType.trading, "notice_success_tmpl_id"),
            template_variable,
            LarkClientType.trading,
        )
        # tt
        pass
    
    def notice_fail(self, sub_title: str, content: str):
        template_variable = {
            "sub_title": sub_title,
            "content": content,
        }
        # trading
        self._send_card(
            self._get_card_tmpl_id(LarkClientType.trading, "notice_fail_tmpl_id"),
            template_variable,
            LarkClientType.trading,
        )
        # tt
        pass
    
    def cron_job_alert(self, cron_func_name:str,  content: str):
        template_variable = {
            "alert_type": "定时任务",
            "function": cron_func_name,
            "content": content,
        }
        # trading
        self._send_card(
            self._get_card_tmpl_id(LarkClientType.trading, "system_alert_tmpl_id"),
            template_variable,
            LarkClientType.trading,
        )
        # tt
        pass
    
    def log_alert(self, log_func_name: str, content: str):
        template_variable = {
            "alert_type": "日志告警",
            "function": log_func_name,
            "content": content,
        }
        # trading
        self._send_card(
            self._get_card_tmpl_id(LarkClientType.trading, "system_alert_tmpl_id"),
            template_variable,
            LarkClientType.trading,
        )
        # tt
        
    def send_card(self, template_id: str, template_variable: Dict[str, Any], client_type: LarkClientType) -> Optional[CreateMessageResponseBody]:
        return self._send_card(template_id, template_variable, client_type)
