import lark_oapi as lark
from lark_oapi.event.callback.model.p2_card_action_trigger import P2CardActionTrigger

from src.infra.clients.lark.spec import MessageHandleSpec




class MessageHandlerAggregate(MessageHandleSpec):
    

    async def aon_message(self, data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        pass
    
    async def aon_action_event(self, data: P2CardActionTrigger) -> None:
        pass
    
    
    async def aon_custom_event(self, data: lark.CustomizedEvent) -> None:
        pass
    


