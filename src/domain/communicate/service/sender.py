from typing import Dict, Any, Optional

from lark_oapi.api.im.v1 import CreateMessageResponseBody

from src.domain.communicate.aggregate.sender import MessageSenderAggregate
from src.infra.clients.lark.client import LarkClientType


class MessageSenderService:
    
    def __init__(self):
        self._message_sender_agg = MessageSenderAggregate()
    
    def notice(self, sub_title: str, content: str):
        self._message_sender_agg.notice(sub_title, content)
    
    def notice_success(self, sub_title: str, content: str):
        self._message_sender_agg.notice_success(sub_title, content)
    
    def notice_fail(self, sub_title: str, content: str):
        self._message_sender_agg.notice_fail(sub_title, content)
    
    def cron_job_alert(self, cron_func_name:str,  content: str):
        self._message_sender_agg.cron_job_alert(cron_func_name, content)
    
    def log_alert(self, log_func_name: str, content: str):
        self._message_sender_agg.log_alert(log_func_name, content)
        
        
    def send_card(self,
                  template_id: str,
                  template_variable: Dict[str, Any],
                  client_type: LarkClientType) -> Optional[CreateMessageResponseBody]:
        return self._message_sender_agg._send_card(template_id, template_variable, client_type)


sender_svc = MessageSenderService()
