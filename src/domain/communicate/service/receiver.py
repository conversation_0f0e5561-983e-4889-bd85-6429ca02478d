from src.domain.communicate.aggregate.handler import MessageHandlerAggregate
from src.infra.clients.lark.receiver import LarkMessageReceiver

__all__ = ["receiver_svc"]

class MessageReceiverService:
    
    
    def __init__(self):
        self._lark_receiver = LarkMessageReceiver(MessageHandlerAggregate())
    
    def start(self):
        self._lark_receiver.start()
    
    def stop(self):
        self._lark_receiver.stop()



receiver_svc = MessageReceiverService()

