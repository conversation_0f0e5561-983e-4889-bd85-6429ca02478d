from pydantic import BaseModel, Field


class ETFInfo(BaseModel):
    # 基金代码	基金简称	基金类型
    code: str = Field(..., description="基金代码")
    name: str = Field(..., description="基金简称")
    type: str = Field(..., description="基金类型")
    
    
class FutureSymbol(BaseModel):
    code: str = Field(..., description="代码")
    name: str = Field(..., description="名称")


class ForeignFutureSymbol(FutureSymbol):
    pass


class FutureSpot(BaseModel):
    code: str = Field(..., description="代码")
    name: str = Field(..., description="名称")
    newest_price: float = Field(..., description="最新价")
    price_change: float = Field(..., description="涨跌额")
    price_change_rate: float = Field(..., description="涨跌幅")
    open_price: float = Field(..., description="开盘价")
    high_price: float = Field(..., description="最高价")
    low_price: float = Field(..., description="最低价")
