from abc import ABC, abstractmethod
from datetime import datetime
from functools import cache
from typing import Union, List, Type

from sqlalchemy import and_

from src.domain.store.repo import dao
from src.domain.schema import MarketTag
from src.infra.app import app
from src.infra.clients.trading.ak import AkShareClient
from src.infra.clients.trading.ts import TushareClient

KLineModel = Union[
    Type[dao.KLine30Min],
    Type[dao.KLine60Min],
    Type[dao.KLineDaily],
]


class KLineStoreAggregate(ABC):

    def __init__(self, model: KLineModel):
        self.model = model
        self.ts_cli = TushareClient()
        self.ak_cli = AkShareClient()

    @abstractmethod
    def list_from_api_with_stock(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[KLineModel]:
        raise NotImplementedError()

    @abstractmethod
    def list_from_api_with_index(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[KLineModel]:
        raise NotImplementedError()

    def list_from_db(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        tag: MarketTag = MarketTag.zh_stock,
    ) -> List[KLineModel]:
        """获取指定股票代码在日期范围内的K线数据"""

        with app.orm_session() as session:
            query = session.query(self.model).filter(
                and_(
                    self.model.code == code,
                    self.model.tag == tag.value,
                    (
                        self.model.datetime >= start_date
                        if hasattr(self.model, "datetime")
                        else self.model.date >= start_date
                    ),
                    (
                        self.model.datetime <= end_date
                        if hasattr(self.model, "datetime")
                        else self.model.date <= end_date
                    ),
                )
            )
            return query.all()

    def save(self, data: List[KLineModel]):
        """保存K线数据列表"""

        with app.orm_session() as session:
            session.add_all(data)
            session.commit()


class KLine30MinStoreAggregate(KLineStoreAggregate):
    def __init__(self):
        super().__init__(model=dao.KLine30Min)

    @cache
    def list_from_api_with_stock(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.KLine30Min]:
        df = self.ts_cli.list_stock_data_with_minute(
            code, start_date, end_date, period="30min"
        )
        resp: List[dao.KLine30Min] = []
        for _, row in df.iterrows():
            resp.append(
                dao.KLine30Min(
                    code=code,
                    open=row["open"],
                    close=row["close"],
                    high=row["high"],
                    low=row["low"],
                    volume=row["vol"],
                    amount=row["amount"],
                    datetime=row["trade_time"],
                )
            )

        return resp

    @cache
    def list_from_api_with_index(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.KLine30Min]:
        df = self.ts_cli.list_index_data_with_minute(
            code, start_date, end_date, period="30min"
        )
        resp: List[dao.KLine30Min] = []
        for _, row in df.iterrows():
            resp.append(
                dao.KLine30Min(
                    code=code,
                    open=row["open"],
                    close=row["close"],
                    high=row["high"],
                    low=row["low"],
                    volume=row["vol"],
                    amount=row["amount"],
                    datetime=row["trade_time"],
                )
            )

        return resp


class KLine60MinStoreAggregate(KLineStoreAggregate):
    def __init__(self):
        super().__init__(model=dao.KLine60Min)

    @cache
    def list_from_api_with_stock(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.KLine60Min]:
        df = self.ts_cli.list_stock_data_with_minute(
            code, start_date, end_date, period="60min"
        )
        resp: List[dao.KLine60Min] = []
        for _, row in df.iterrows():
            resp.append(
                dao.KLine60Min(
                    code=code,
                    open=row["open"],
                    close=row["close"],
                    high=row["high"],
                    low=row["low"],
                    volume=row["vol"],
                    amount=row["amount"],
                    datetime=row["trade_time"],
                )
            )
        return resp

    @cache
    def list_from_api_with_index(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.KLine60Min]:
        df = self.ts_cli.list_index_data_with_minute(
            code, start_date, end_date, period="60min"
        )
        resp: List[dao.KLine60Min] = []
        for _, row in df.iterrows():
            resp.append(
                dao.KLine60Min(
                    code=code,
                    open=row["open"],
                    close=row["close"],
                    high=row["high"],
                    low=row["low"],
                    volume=row["vol"],
                    amount=row["amount"],
                    datetime=row["trade_time"],
                )
            )
        return resp


class KLineDailyStoreAggregate(KLineStoreAggregate):
    def __init__(self):
        super().__init__(model=dao.KLineDaily)

    @cache
    def list_from_api_with_stock(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.KLineDaily]:
        df = self.ts_cli.list_stock_data_with_daily(code, start_date, end_date)
        resp: List[dao.KLineDaily] = []
        for _, row in df.iterrows():
            resp.append(
                dao.KLineDaily(
                    code=code,
                    open=row["open"],
                    close=row["close"],
                    high=row["high"],
                    low=row["low"],
                    volume=row["vol"],
                    amount=row["amount"],
                    date=row["trade_date"],
                )
            )
        return resp

    @cache
    def list_from_api_with_index(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.KLineDaily]:
        df = self.ak_cli.list_index_data_with_daily(
            code=code, start_date=start_date, end_date=end_date, period="daily"
        )
        resp: List[dao.KLineDaily] = []
        for _, row in df.iterrows():
            resp.append(
                dao.KLineDaily(
                    code=code,
                    open=row["开盘"],
                    close=row["收盘"],
                    high=row["最高"],
                    low=row["最低"],
                    volume=row["成交量"],
                    amount=row["成交额"],
                    date=row["日期"],
                )
            )
        return resp
