from datetime import datetime
from typing import List, Literal

from loguru import logger

from src.domain.store.aggregate.zh.kline import KLine30MinStoreAggregate, KLine60MinStoreAggregate, \
    KLineDailyStoreAggregate, KLineStoreAggregate
from src.domain.store.repo import dao
from src.domain.schema import MarketTag
from src.infra.app import app
from src.infra.clients.mysql.orm import bulk_insert
from src.infra.clients.trading.ak import AkShareClient
from src.infra.clients.trading.mine import MyClient
from src.infra.clients.trading.utils import get_zh_today


class ZhIndexStoreAggregate:

    def __init__(self):
        self._ak_share_client = AkShareClient()
        self._my_client = MyClient()
        self.kline_30min = KLine30MinStoreAggregate()
        self.kline_60min = KLine60MinStoreAggregate()
        self.kline_daily = KLineDailyStoreAggregate()

    def _update_single_index_kline(
        self, code: str, start_date: datetime, end_date: datetime
    ):
        """更新单个指数的所有K线数据，确保code和时间/日期唯一，避免重复更新"""
        try:

            # 更新日K线
            logger.info(f"开始更新指数 {code} 的日K线数据")
            self._update_kline_data(
                code, start_date, end_date, self.kline_daily, "date"
            )

        except Exception as e:
            # 记录错误但继续处理其他指数
            logger.exception(f"更新指数 {code} 的K线数据时出错: {e}")

    def _update_kline_data(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        kline_store: KLineStoreAggregate,
        date_field: str,
    ):
        db_data = kline_store.list_from_db(
            code, start_date, end_date, tag=MarketTag.zh_index
        )
        if db_data:
            logger.info(f"数据库中已有 {len(db_data)} 条股票 {code} 的K线数据")
            return

        api_data = kline_store.list_from_api_with_index(code, start_date, end_date)

        if not api_data:
            return

        # 创建现有数据的唯一键集合 (code, datetime/date)
        existing_keys = set()
        for item in db_data:
            time_value = getattr(item, date_field)
            existing_keys.add((item.code, time_value))

        new_data = []
        for item in api_data:
            time_value = getattr(item, date_field)
            if (item.code, time_value) not in existing_keys:
                item.tag = MarketTag.zh_index.value
                new_data.append(item)

        with app.orm_session() as session:
            bulk_insert(session, kline_store.model, new_data)
            session.commit()

    def list_index_from_db(self) -> List[dao.ZhIndex]:
        with app.orm_session() as session:
            return session.query(dao.ZhIndex).all()

    def list_index_from_api(
        self,
        choice_type: Literal[
            "沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"
        ],
    ) -> List[dao.ZhIndex]:
        df = self._ak_share_client.list_main_stock_index(choice_type)
        resp: List[dao.ZhIndex] = []
        for _, row in df.iterrows():
            resp.append(
                dao.ZhIndex(
                    code=row["代码"],
                    name=row["名称"],
                    newest_price=row["最新价"],
                    price_change=row["涨跌额"],
                    price_change_rate=row["涨跌幅"],
                    volume=row["成交量"],
                    amount=row["成交额"],
                )
            )
        return resp

    # 手动添加指数
    def add_index(
        self,
        code: str,
        choice_type: Literal[
            "沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"
        ],
    ):
        # 检查是否已经存在
        with app.orm_session() as session:
            existing_index = (
                session.query(dao.ZhIndex).filter(dao.ZhIndex.code == code).first()
            )
            if existing_index:
                logger.warning(f"指数 {code} 已经存在，跳过添加")
                return

        df = self._ak_share_client.list_main_stock_index(choice_type)
        row = df[df["代码"] == code].iloc[0]
        with app.orm_session() as session:
            index = dao.ZhIndex(
                code=row["代码"],
                name=row["名称"],
                newest_price=row["最新价"],
                price_change=row["涨跌额"],
                price_change_rate=row["涨跌幅"],
                volume=row["成交量"],
                amount=row["成交额"],
            )
            session.add(index)
            session.commit()

    def delete_index(self, code: str):
        with app.orm_session() as session:
            session.query(dao.ZhIndex).filter(dao.ZhIndex.code == code).delete()
            session.commit()

    def just_update_index_quotes(self):
        """优化的指数行情数据更新方法，只更新数据库存在的指数标的信息"""
        # 获取数据库中已有的指数记录
        index_list = self.list_index_from_db()
        if not index_list:
            logger.info("数据库中没有指数记录，请先添加指数")
            return

        # 获取指数代码列表
        total_indices = len(index_list)
        logger.info(f"开始更新 {total_indices} 个指数的行情数据")

        today = get_zh_today()
        for index_obj in index_list:
            df = self._ak_share_client.list_index_data_with_daily(
                index_obj.code,  start_date=today, end_date=today, period="daily",
            )
            if df.empty:
                logger.warning(f"未找到指数 {index_obj.code} 的行情数据")
                continue
            logger.info(f"更新指数 {index_obj.code} {index_obj.name} 的行情数据")
            row = df.iloc[0]
            index_obj.newest_price = row["收盘"]
            index_obj.price_change = row["涨跌额"]
            index_obj.price_change_rate = row["涨跌幅"]
            index_obj.volume = row["成交量"]
            index_obj.amount = row["成交额"]
            with app.orm_session() as session:
                session.add(index_obj)
                session.commit()

    def update_index_all_info(
        self, start_date: datetime, end_date: datetime, is_update_quotes: bool = True
    ):
        """更新指数的所有信息，包括行情和K线数据"""
        # 是否更新指数行情数据
        if is_update_quotes:
            self.just_update_index_quotes()

        # 获取所有指数代码
        index_list = self.list_index_from_db()
        if not index_list:
            logger.info("数据库中没有指数记录，请先添加指数")
            return

        index_codes = [index.code for index in index_list]
        total_indices = len(index_codes)
        logger.info(f"开始更新 {total_indices} 个指数的K线数据")

        # 更新每个指数的K线数据，并显示进度
        for idx, code in enumerate(index_codes):
            # 每5个指数输出一次进度
            if idx % 5 == 0:
                logger.info(
                    f"K线数据更新进度: {idx}/{total_indices} ({idx / total_indices * 100:.1f}%)"
                )

            try:
                self._update_single_index_kline(code, start_date, end_date)
            except Exception as e:
                logger.exception(f"更新指数 {code} 的K线数据时出错: {e}")

        logger.info(f"指数数据更新完成，共处理 {total_indices} 个指数")
