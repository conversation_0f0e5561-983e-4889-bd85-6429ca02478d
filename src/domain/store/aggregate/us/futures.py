from typing import List

from src.domain.store.repo import do
from src.infra.clients.trading.ak import AkShareClient


class UsFuturesStoreAggregate:
    
    
    def __init__(self):
        self._ak_share_client = AkShareClient()
        
        
    def list_futures_symbol_from_api(self) -> List[do.ForeignFutureSymbol]:
        df = self._ak_share_client.list_foreign_futures_symbol()
        resp: List[do.ForeignFutureSymbol] = []
        for _, row in df.iterrows():
            resp.append(
                do.ForeignFutureSymbol(
                    code=row["code"],
                    name=row["symbol"],
                )
            )
        return resp
    
    def list_futures_quote_from_api(self, symbol_list: List[str]) -> List[do.FutureSpot]:
        df = self._ak_share_client.list_foreign_futures_quote(symbol_list)
        resp: List[do.FutureSpot] = []
        for _, row in df.iterrows():
            resp.append(
                do.FutureSpot(
                    code=row["代码"],
                    name=row["名称"],
                    newest_price=row["最新价"],
                    price_change=row["涨跌额"],
                    price_change_rate=row["涨跌幅"],
                    high_price=row["最高价"],
                    low_price=row["最低价"],
                    open_price=row["开盘价 "],
                )
            )
        return resp
