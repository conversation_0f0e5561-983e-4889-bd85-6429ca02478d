from datetime import datetime
from typing import List

from loguru import logger

from src.domain.store.aggregate.zh.kline import KLineDailyStoreAggregate
from src.domain.store.repo import dao
from src.domain.schema import MarketTag
from src.infra.app import app
from src.infra.clients.trading.ak import AkShareClient
from src.infra.clients.trading.mine import MyClient
from src.infra.clients.trading.utils import transform_nan_to_none


# Todo 未经测试 （暂时搁置）
class UsIndexStoreAggregate:

    def __init__(self):
        self._ak_share_client = AkShareClient()
        self._my_client = MyClient()
        self.kline_daily = KLineDailyStoreAggregate()

    def list_index_from_db(self) -> List[dao.UsStock]:
        with app.orm_session() as session:
            return session.query(dao.UsStock).filter(dao.UsStock.is_index == True).all()

    def just_update_index_quote(self):
        """
        更新数据库中的指数行情数据
        """
        df = self._my_client.list_us_stock_index_quote()

        with app.orm_session() as session:
            # 一次性获取所有现有指数记录
            existing_indices = session.query(dao.UsStock).all()

            if not existing_indices:
                logger.info("数据库中没有指数记录，请先添加指数")
                return

            # 更新已经存在的指数行情
            for index in existing_indices:
                code = index.code
                row = df[df["代码"] == code].iloc[0]
                row_dict = transform_nan_to_none(row)
                index.name = row_dict["名称"]
                index.newest_price = row_dict["最新价"]
                index.price_change = row_dict["涨跌额"]
                index.price_change_rate = row_dict["涨跌幅"]
                index.is_index = True
                session.add(index)
            session.commit()

    def update_index_all_info(
        self, start_date: datetime, end_date: datetime, is_update_quotes: bool = True
    ):
        """更新指数的所有信息，包括行情和K线数据"""
        # 是否更新指数行情数据
        if is_update_quotes:
            self.just_update_index_quote()

        # 获取所有指数代码
        index_list = self.list_index_from_db()
        if not index_list:
            logger.info("数据库中没有指数记录，请先添加指数")
            return
        # 只需要写入日线数据

        index_codes = [index.code for index in index_list]
        total_indices = len(index_codes)
        logger.info(f"开始更新 {total_indices} 个美股指数的K线数据")

        # 更新每个指数的K线数据，并显示进度
        for idx, code in enumerate(index_codes):
            # 每个指数输出一次进度
            logger.info(
                f"K线数据更新进度: {idx + 1}/{total_indices} ({(idx + 1) / total_indices * 100:.1f}%)"
            )

            try:
                self._update_single_index_kline(code, start_date, end_date)
            except Exception as e:
                logger.error(f"更新美股指数 {code} 的K线数据时出错: {e}")

        logger.info(f"美股指数K线数据更新完成，共处理 {total_indices} 个指数")

    def _update_single_index_kline(
        self, code: str, start_date: datetime, end_date: datetime
    ):
        """更新单个美股指数的K线数据，确保code和日期唯一，避免重复更新"""
        try:
            # 美股指数只更新日K线
            self._update_kline_data(
                code, start_date, end_date, self.kline_daily, "date"
            )
        except Exception as e:
            # 记录错误但继续处理其他指数
            logger.error(f"更新美股指数 {code} 的K线数据时出错: {e}")
            raise e

    def _update_kline_data(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        kline_store: KLineDailyStoreAggregate,
        date_field: str,
    ):
        """更新特定类型的K线数据，确保code和日期唯一，避免重复更新"""
        # 从API获取K线数据
        try:
            # 使用美股指数专用的API方法获取数据
            df = self._ak_share_client.list_us_index_with_daily(symbol=code)

            # 过滤日期范围
            df = df[(df.index >= start_date) & (df.index <= end_date)]

            # 构建K线数据对象
            api_data = []
            for date, row in df.iterrows():
                kline_data = kline_store.model(
                    code=code,
                    open=row["open"],
                    close=row["close"],
                    high=row["high"],
                    low=row["low"],
                    volume=row["volume"],
                    amount=row["amount"],
                    date=date,
                )
                api_data.append(kline_data)

            if not api_data:
                logger.info(f"未获取到美股指数 {code} 在指定日期范围内的K线数据")
                return
        except Exception as e:
            logger.error(f"获取美股指数 {code} 的K线数据时出错: {e}")
            return

        # 从数据库获取现有数据
        db_data = kline_store.list_from_db(
            code, start_date, end_date, MarketTag.us_index
        )

        # 创建现有数据的唯一键集合 (code, date)
        existing_keys = set()
        for item in db_data:
            time_value = getattr(item, date_field)
            existing_keys.add((item.code, time_value))

        # 过滤出需要添加的新数据
        new_data = []
        for item in api_data:
            time_value = getattr(item, date_field)
            if (item.code, time_value) not in existing_keys:
                new_data.append(item)

        # 只保存新数据，避免更新已存在的记录
        if new_data:
            with app.orm_session() as session:
                session.add_all(new_data)
                session.commit()
            logger.info(f"添加了 {len(new_data)} 条美股指数 {code} 的K线数据")
