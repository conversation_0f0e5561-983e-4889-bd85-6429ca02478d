from typing import List, Optional

from loguru import logger

from src.domain.monitor.aggregate.base import BaseMonitorAggregate
from src.domain.monitor.repo import dao
from src.domain.monitor.repo import do
from src.domain.monitor.schema import MonitorType
from src.domain.schema import MarketTag
from src.infra.app import app


class PriceMonitorAggregate(BaseMonitorAggregate):
    
    
    def __init__(self):
        pass
    
   


class ZhPriceMonitor:
    
    def __init__(self):
        self._
