from datetime import datetime

import pandas as pd
import tushare as ts
from tushare.pro.client import DataApi

from src.infra.clients.trading.schema import TushareMinPeriod, DateTimeFormat
from src.infra.clients.trading.utils import get_zh_stock_market, get_index_market
from src.infra.utils import retry_network


class TushareClient:
    TOKEN = "f60827dcc933679a892b5bc8cd1bf3c8ebd541f3b7468208b925bc65"

    def __init__(self):
        self.pro: DataApi = ts.pro_api(self.TOKEN)

    def _transform_stock_2_ts_code(self, code: str) -> str:
        market = get_zh_stock_market(code)
        if market == "sh":
            return f"{code}.SH"
        elif market == "sz":
            return f"{code}.SZ"
        else:
            raise ValueError(f"不支持的股票代码: {code}")

    def _transform_index_2_stock(self, index_code: str) -> str:
        market = get_index_market(index_code)
        if market == "sh":
            return f"{index_code}.SH"
        elif market == "sz":
            return f"{index_code}.SZ"
        else:
            raise ValueError(f"不支持的指数代码: {index_code}")

    @retry_network
    def list_stock_data_with_minute(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        period: TushareMinPeriod = "30min",
    ) -> pd.DataFrame:
        """
        获取股票分钟线数据
        :return: ts_code, trade_time, close, open, high, low, vol, amount
        """
        ts_code = self._transform_stock_2_ts_code(code)
        start_date_str = start_date.strftime(DateTimeFormat.DATETIME_FORMAT_1.value)
        end_date_str = end_date.strftime(DateTimeFormat.DATETIME_FORMAT_1.value)
        return self.pro.stk_mins(
            **{
                "ts_code": ts_code,
                "freq": period,
                "start_date": start_date_str,
                "end_date": end_date_str,
                "limit": "",
                "offset": "",
            },
            fields=[
                "ts_code",
                "trade_time",
                "close",
                "open",
                "high",
                "low",
                "vol",
                "amount",
            ],
        )

    @retry_network
    def list_stock_data_with_daily(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> pd.DataFrame:
        """
         获取股票日线数据
        :return: ts_code	trade_date	open	high	low	close	pre_close	change	pct_chg	vol	amount
        """
        ts_code = self._transform_stock_2_ts_code(code)
        start_date_str = start_date.strftime(DateTimeFormat.DATE_FORMAT_1.value)
        end_date_str = end_date.strftime(DateTimeFormat.DATE_FORMAT_1.value)
        return self.pro.daily(
            ts_code=ts_code, start_date=start_date_str, end_date=end_date_str
        )

    @retry_network
    def list_index_data_with_minute(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        period: TushareMinPeriod = "30min",
    ) -> pd.DataFrame:
        """
        获取指数分钟线数据
        :return: ts_code, trade_time, close, open, high, low, vol, amount
        """
        ts_code = self._transform_index_2_stock(code)
        start_date_str = start_date.strftime(DateTimeFormat.DATETIME_FORMAT_1.value)
        end_date_str = end_date.strftime(DateTimeFormat.DATETIME_FORMAT_1.value)
        return self.pro.index_mins(
            **{
                "ts_code": ts_code,
                "freq": period,
                "start_date": start_date_str,
                "end_date": end_date_str,
                "limit": "",
                "offset": "",
            },
            fields=[
                "ts_code",
                "trade_time",
                "close",
                "open",
                "high",
                "low",
                "vol",
                "amount",
            ],
        )
