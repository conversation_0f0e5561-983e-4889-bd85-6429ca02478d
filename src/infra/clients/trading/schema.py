from enum import Enum
from typing import Literal, Union

DongCaiMinPeriod = Literal["1", "5", "15", "30", "60"]
DongCaiDailyPeriod = Literal["daily", "weekly", "monthly"]
DongCaiPeriod = Union[DongCaiMinPeriod, DongCaiDailyPeriod]
# 前复权，后复权
DongCaiFQ = Literal["qfq", "hfq", ""]

# Tushare
TushareMinPeriod = Literal["1min", "5min", "15min", "30min", "60min"]


class DateTimeFormat(str, Enum):
    DATE_FORMAT_1 = "%Y%m%d"
    DATE_FORMAT_2 = "%Y-%m-%d"
    DATETIME_FORMAT_1 = "%Y-%m-%d %H:%M:%S"
    DATETIME_FORMAT_2 = "%Y-%m-%d %H:%M"
    TIME_FORMAT_1 = "%H:%M:%S"
    TIME_FORMAT_2 = "%H:%M"


class MinuteInterval(str, Enum):
    min_1 = "1"
    min_5 = "5"
    min_15 = "15"
    min_30 = "30"
    min_60 = "60"


class AStockMarket(str, Enum):
    SH = "sh"
    SZ = "sz"
    BJ = "bj"


class PriceUMAUpAction(str, Enum):
    FIRE_JET = 8201, "火箭发射"
    RAPID_REBOUND = 8202, "快速反弹"
    LARGE_VOLUME_BUY = 8193, "大笔买入"
    SEAL_STOP = 4, "封涨停板"
    OPENING_LIMIT = 32, "打开跌停板"
    LARGE_BUY = 64, "有大买盘"
    COMPETITIVE_RISE = 8207, "竞价上涨"
    HIGH_FIVE_DAY_LINE = 8209, "高开5日线"
    UPWARD_BREAK = 8211, "向上缺口"
    NEW_60_DAY_HIGH = 8213, "60日新高"
    LARGE_SCALE_UP = 8215, "60日大幅上涨"

    def __new__(cls, value, description):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj

    def __str__(self):
        return f"{self._value_}"

    @classmethod
    def list_values(cls):
        return [e._value_ for e in cls]


class PriceUMADownAction(str, Enum):
    ACCELERATE_DROP = 8204, "加速下跌"
    HIGH_FALL_WATER = 8203, "高台跳水"
    LARGE_SELL = 8194, "大笔卖出"
    BLOCK_DOWN_LIMIT = 8, "封跌停板"
    OPEN_RISE_LIMIT = 16, "打开涨停板"
    LARGE_SELL_ORDER = 128, "有大卖盘"
    COMPETITIVE_DECREASE = 8208, "竞价下跌"
    LOW_OPEN_FIVE_DAY_LINE = 8210, "低开5日线"
    DOWNWARD_GAP = 8212, "向下缺口"
    NEW_60_DAY_LOW = 8214, "60日新低"
    LARGE_SCALE_DROP_60_DAYS = 8216, "60日大幅下跌"

    def __new__(cls, value, description):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj

    def __str__(self):
        return f"{self._value_}"

    @classmethod
    def list_values(cls):
        return [e._value_ for e in cls]
