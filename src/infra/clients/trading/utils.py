from datetime import datetime, timedelta, time
from typing import Dict, Any

import akshare as ak
import pandas as pd


from src.infra.clients.trading.schema import AStockMarket


def get_trading_date() -> pd.DataFrame:
    return ak.tool_trade_date_hist_sina()




def is_zh_trading_time_range(today: datetime) -> bool:
    """
    判断指定时间是否在 A 股连续竞价交易时段
    规则：
    - 工作日（周一至周五，排除节假日）
    - 上午时段：09:30:00 - 11:30:00（含边界）
    - 下午时段：13:00:00 - 14:57:00（沪市）/ 13:00:00 - 15:00:00（深市）
    - 本函数取沪深交易所的交集：13:00:00 - 14:57:00
    """


    # 2. 获取当前时间对象
    current_time = today.time()

    # 3. 定义交易时段（精确到秒）
    morning_start = time(9, 30, 0)
    morning_end = time(11, 30, 0)
    afternoon_start = time(13, 0, 0)
    afternoon_end = time(14, 57, 0)  # 取沪深交易所的交集

    # 4. 时间区间判断
    return (
            (morning_start <= current_time <= morning_end) or
            (afternoon_start <= current_time <= afternoon_end)
    )

    


# 获取最近一个有效的交易日期
def get_zh_latest_trading_date() -> datetime:
    today = datetime.now()
    while True:
        if today.date() in get_trading_date()["trade_date"].values:
            return today

        today = today - timedelta(days=1)


def is_zh_trading_date(today: datetime) -> bool:
    return today.date() in get_trading_date()["trade_date"].values


# 获取最近一个已经结束交易的交易日
def get_zh_today_with_finished() -> datetime:
    today = get_zh_latest_trading_date()
    now_time = datetime.now()
    if today.date() == now_time.date():
        if now_time.hour < 15:
            while True:
                today = today - timedelta(days=1)
                if is_zh_trading_date(today):
                    return today
    return today


# 获取最近一个交易时间
def get_zh_latest_trading_datetime() -> datetime:
    trading_date = get_zh_latest_trading_date()
    if is_zh_trading_time_range(trading_date):
        
        return trading_date
    
    # 如果是 11.30 到 13.00 之间，返回 11.30
    morning_start = time(9, 30, 0)
    morning_end = time(11, 30, 0)
    
    return trading_date.replace(hour=15, minute=0, second=0)


def get_zh_today_before(before_days: int) -> datetime:
    today = get_zh_latest_trading_datetime()
    while True:
        today = today - timedelta(days=1)
        if is_zh_trading_date(today):
            before_days -= 1
            if before_days == 0:
                return today


def get_datetime_range(before_days=30) -> (datetime, datetime):
    start_date = get_zh_today_with_finished() - timedelta(days=before_days)
    end_date = datetime.now()
    return start_date, end_date

def get_zh_index_market(code: str) -> str:
    """
    判断指数代码属于沪深哪个市场
    参数支持：字符串或数字格式的指数代码（如 '000001' 或 '399001'）
    返回：'sz'/'sh'/'unknown'

    常见指数代码规则：
    - 上证指数：000001-000999 (上证综指等)
    - 上证系列：000001-999999 (沪深300为000300，中证500为000905等)
    - 深证指数：399001-399999 (深证成指为399001，创业板指为399006等)
    """
    # 提取纯数字并补齐6位
    code_str = "".join(filter(str.isdigit, code))
    code_str = code_str.zfill(6)

    # 深证指数判断（399开头为深证指数）
    if code_str.startswith("399"):
        return AStockMarket.SZ.value

    # 沪深300、中证500等指数判断（000300沪深300，000905中证500等）
    elif code_str == "000300" or code_str == "000905" or code_str == "000852":
        return AStockMarket.SH.value

    # 上证指数判断（000001上证指数等）
    elif code_str.startswith("000"):
        # 特殊处理：000001-000999范围内的指数通常是上证系列指数
        if int(code_str) < 1000:
            return AStockMarket.SH.value
        # 000开头但不在特殊范围内的，默认为深市
        return AStockMarket.SZ.value

    # 上证系列指数判断（51开头为上证等权等）
    elif code_str.startswith("51"):
        return AStockMarket.SH.value

    # 其他情况
    else:
        return "unknown"


def get_zh_stock_market(code: str) -> str:
    """
    判断股票代码属于沪深京哪个市场
    参数支持：字符串或数字格式的股票代码（如 '600000' 或 600000）
    返回：'sz'/'sh'/'bj'/'unknow'
    """
    # 提取纯数字并补齐6位
    code_str = "".join(filter(str.isdigit, code))
    code_str = code_str.zfill(6)  # 处理短代码如北交所代码'43'

    # 沪市判断（60开头主板，688开头科创板）
    if code_str.startswith(("60", "688")):
        return AStockMarket.SH.value

    # 深市判断（000/001/002/003主板，300/301创业板）
    elif code_str[:3] in {"000", "001", "002", "003"} or code_str.startswith(
        ("300", "301")
    ):
        return AStockMarket.SZ.value

    # 京市判断（北交所：43/83/87/88开头）
    elif code_str.startswith(("43", "83", "87", "88")):
        return AStockMarket.BJ.value

    # 其他情况
    else:
        return "unknown"

def transform_nan_to_none(df: pd.DataFrame) -> pd.DataFrame:
    return df.replace({float("nan"): None})


def transform_row_2_dict(row: pd.Series) -> Dict[str, Any]:
    row_dict = row.to_dict()
    for key in row_dict:
        if pd.isna(row_dict[key]):
            row_dict[key] = None
    return row_dict


if __name__ == "__main__":
    print(get_zh_latest_trading_datetime())