import argparse
import os
import sys

from loguru import logger


__all__ = ["logger"]

from config.globalconfig import get_or_create_settings_ins


def init_logger():
    config = get_or_create_settings_ins()
    logger.remove()
    parser = argparse.ArgumentParser()
    parser.add_argument("--log", help="log level", default=config.app.log_level)
    args, _ = parser.parse_known_args()
    logger.add(sys.stdout, level=args.log)








