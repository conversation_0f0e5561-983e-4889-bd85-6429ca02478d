"""init

Revision ID: c1f55a84c0c3
Revises: 
Create Date: 2025-05-24 11:52:34.945572

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c1f55a84c0c3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('guanfu_monitor_object',
    sa.Column('code', sa.VARCHAR(length=12), nullable=False, comment='股票代码'),
    sa.Column('name', sa.VARCHAR(length=64), nullable=False, comment='股票名称'),
    sa.Column('market_tag', sa.VARCHAR(length=16), nullable=False, comment='标签, zh_stock, zh_index, zh_etf, hk_stock, us_stock ...'),
    sa.Column('monitor_type', sa.VARCHAR(length=32), nullable=False, comment='监控类型, price, volume, abnormal_fund ...'),
    sa.Column('monitor_arg', sa.VARCHAR(length=2048), nullable=False, comment='监控参数'),
    sa.Column('id', mysql.BIGINT(unsigned=True), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建日期'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新日期'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_guanfu_monitor_object_code'), 'guanfu_monitor_object', ['code'], unique=False)
    op.create_index(op.f('ix_guanfu_monitor_object_market_tag'), 'guanfu_monitor_object', ['market_tag'], unique=False)
    op.drop_index('ix_apscheduler_jobs_next_run_time', table_name='apscheduler_jobs')
    op.drop_table('apscheduler_jobs')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('apscheduler_jobs',
    sa.Column('id', mysql.VARCHAR(collation='utf8mb4_general_ci', length=191), nullable=False),
    sa.Column('next_run_time', mysql.DOUBLE(asdecimal=True), nullable=True),
    sa.Column('job_state', sa.BLOB(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_apscheduler_jobs_next_run_time', 'apscheduler_jobs', ['next_run_time'], unique=False)
    op.drop_index(op.f('ix_guanfu_monitor_object_market_tag'), table_name='guanfu_monitor_object')
    op.drop_index(op.f('ix_guanfu_monitor_object_code'), table_name='guanfu_monitor_object')
    op.drop_table('guanfu_monitor_object')
    # ### end Alembic commands ###
